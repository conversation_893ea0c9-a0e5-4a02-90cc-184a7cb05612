import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  TextField,
  InputAdornment,
  Tooltip,
  useTheme,
  useMediaQuery,
  Stack
} from '@mui/material';
import {
  GroupWork,
  Delete,
  Visibility,
  Refresh,
  Search,
  Clear,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { consumerGroupsApi } from '../services/api';
import { useDebounce } from '../hooks/useDebounce';
import { FixedSizeList as List } from 'react-window';

const ConsumerGroupCard = ({ group, onView, onDelete }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        '&:hover': {
          boxShadow: theme.shadows[4],
          transform: 'translateY(-1px)',
          transition: 'all 0.2s ease-in-out'
        }
      }}
    >
      <CardContent sx={{ flexGrow: 1, p: { xs: 2, sm: 3 } }}>
        {/* Group ID Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <GroupWork sx={{
            mr: 1,
            color: 'primary.main',
            fontSize: { xs: 20, sm: 24 }
          }} />
          <Typography
            variant={isMobile ? "subtitle1" : "h6"}
            component="h2"
            sx={{
              fontSize: { xs: '1rem', sm: '1.25rem' },
              fontWeight: 600,
              wordBreak: 'break-word',
            }}
          >
            {group.groupId}
          </Typography>
        </Box>

        {/* Status Chips */}
        <Stack
          direction={isMobile ? "column" : "row"}
          spacing={1}
          sx={{
            mb: 2,
            alignItems: isMobile ? "flex-start" : "center"
          }}
        >
          <Chip
            label={`State: ${group.state}`}
            size="small"
            color={group.state === 'Stable' ? 'success' : 'warning'}
            variant="outlined"
            sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
          />
          <Chip
            label={`Members: ${group.members?.length || 0}`}
            size="small"
            color="primary"
            variant="outlined"
            sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
          />
          <Chip
            label={`Protocol: ${group.protocolType}`}
            size="small"
            color="secondary"
            variant="outlined"
            sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
          />
        </Stack>

        {/* Actions */}
        <Stack
          direction={isMobile ? "column" : "row"}
          spacing={1}
          sx={{
            mt: 'auto',
            alignItems: isMobile ? "stretch" : "center"
          }}
        >
          <Button
            size="small"
            startIcon={<Visibility />}
            onClick={() => onView(group.groupId)}
            fullWidth={isMobile}
            sx={{
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              px: { xs: 1.5, sm: 2 },
            }}
          >
            View Details
          </Button>
          <Tooltip title="Delete Group">
            <IconButton
              size="small"
              onClick={() => onDelete(group.groupId)}
              color="error"
              sx={{
                alignSelf: isMobile ? "flex-end" : "center",
                p: { xs: 0.5, sm: 1 },
                width: { xs: 32, sm: 36 },
                height: { xs: 32, sm: 36 },
              }}
            >
              <Delete fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      </CardContent>
    </Card>
  );
};

const ConsumerGroups = () => {
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: consumerGroups, isLoading } = useQuery(
    'consumer-groups',
    consumerGroupsApi.getAll,
    {
      refetchInterval: 30000,
    }
  );

  // Debounce search term to avoid excessive filtering
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Filter consumer groups based on debounced search term
  const filteredGroups = useMemo(() => {
    if (!consumerGroups?.data || !debouncedSearchTerm.trim()) {
      return consumerGroups?.data || [];
    }

    return consumerGroups.data.filter(group =>
      group.groupId.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  }, [consumerGroups?.data, debouncedSearchTerm]);

  const deleteMutation = useMutation(consumerGroupsApi.delete, {
    onSuccess: () => {
      toast.success('Consumer group deleted successfully');
      queryClient.invalidateQueries('consumer-groups');
      setDeleteConfirmOpen(false);
      setGroupToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting consumer group: ${error.message}`);
    },
  });

  const handleDeleteGroup = (groupId) => {
    setGroupToDelete(groupId);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (groupToDelete) {
      deleteMutation.mutate(groupToDelete);
    }
  };

  const handleViewGroup = (groupId) => {
    navigate(`/consumer-groups/${groupId}`);
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'stretch', sm: 'center' },
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography
          variant={isMobile ? "h5" : "h4"}
          sx={{
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          Consumer Groups
        </Typography>
        <Button
          variant="outlined"
          startIcon={<Refresh />}
          onClick={() => queryClient.invalidateQueries('consumer-groups')}
          size={isMobile ? "small" : "medium"}
          fullWidth={isMobile}
        >
          Refresh
        </Button>
      </Box>

      {/* Search Bar */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Search consumer groups..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size="medium"
        />
      </Box>

      {/* Results Info */}
      {debouncedSearchTerm && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {filteredGroups.length} consumer group(s) found for "{debouncedSearchTerm}"
          </Typography>
        </Box>
      )}

      {consumerGroups?.data?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No consumer groups found.
        </Alert>
      ) : filteredGroups.length === 0 && debouncedSearchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No consumer groups found matching "{debouncedSearchTerm}". Try a different search term.
        </Alert>
      ) : (
        <List
          height={Math.min(900, window.innerHeight - 300)}
          itemCount={filteredGroups.length}
          itemSize={isMobile ? 180 : 130}
          width="100%"
        >
          {({ index, style }) => {
            const group = filteredGroups[index];
            return (
              <div style={style} key={group.groupId}>
                <ConsumerGroupCard
                  group={group}
                  onView={handleViewGroup}
                  onDelete={handleDeleteGroup}
                />
              </div>
            );
          }}
        </List>
      )}

      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete consumer group "{groupToDelete}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConsumerGroups; 