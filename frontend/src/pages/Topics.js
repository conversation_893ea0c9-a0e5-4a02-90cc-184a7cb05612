import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Stack,
} from '@mui/material';
import {
  Add,
  // Edit,
  Delete,
  Visibility,
  Topic as TopicIcon,
  Settings,
  Search,
  Clear,
  Message as MessageIcon,
  Refresh,
} from '@mui/icons-material';
import { useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { topicsApi } from '../services/api';
// import { useDebounce } from '../hooks/useDebounce';
import { useTopics } from '../hooks/useTopics';
import { FixedSizeList as List } from 'react-window';

// Utility function to format numbers
const formatNumber = (num) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

const useFilteredTopics = (topics = [], searchTerm = '') => {
  const [filtered, setFiltered] = useState(topics);

  useEffect(() => {
    if (!searchTerm || !topics.length) {
      setFiltered(topics);
      return;
    }

    const timeout = setTimeout(() => {
      const lower = searchTerm.toLowerCase();
      const result = topics.filter((t) =>
        t.name.toLowerCase().includes(lower)
      );
      setFiltered(result);
    }, 100);

    return () => clearTimeout(timeout);
  }, [topics, searchTerm]);

  return filtered;
};

const CreateTopicDialog = ({ open, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    numPartitions: 1,
    replicationFactor: 1,
    configs: [],
  });

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    if (!formData.name.trim()) {
      toast.error('Topic name is required');
      return;
    }
    onSubmit(formData);
    setFormData({ name: '', numPartitions: 1, replicationFactor: 1, configs: [] });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Topic</DialogTitle>
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          <TextField
            fullWidth
            label="Topic Name"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
          <TextField
            fullWidth
            label="Number of Partitions"
            type="number"
            value={formData.numPartitions}
            onChange={(e) => handleChange('numPartitions', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
          <TextField
            fullWidth
            label="Replication Factor"
            type="number"
            value={formData.replicationFactor}
            onChange={(e) => handleChange('replicationFactor', parseInt(e.target.value))}
            inputProps={{ min: 1 }}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained">
          Create Topic
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const TopicCard = ({ topic, onEdit, onDelete, onView, onConfigure, onLoadMessageCount }) => {
  const [isLoadingCount, setIsLoadingCount] = useState(false);
  const [messageCount, setMessageCount] = useState(topic.totalMessages);
  const [partitionDetails, setPartitionDetails] = useState(topic.partitionDetails);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num?.toString() || '0';
  };

  const handleLoadMessageCount = async () => {
    if (messageCount !== undefined && !isLoadingCount) return; // Already loaded

    setIsLoadingCount(true);
    try {
      const response = await topicsApi.getMessageCount(topic.name);
      setMessageCount(response.data.totalMessages);
      setPartitionDetails(response.data.partitionDetails);
      toast.success(`Message count loaded for ${topic.name}`);
    } catch (error) {
      toast.error(`Failed to load message count: ${error.message}`);
    } finally {
      setIsLoadingCount(false);
    }
  };

  return (
    <Card sx={{
      p: { xs: 2, sm: 3 },
      display: 'flex',
      flexDirection: { xs: 'column', sm: 'row' },
      alignItems: { xs: 'stretch', sm: 'center' },
      gap: 2,
      '&:hover': {
        boxShadow: theme.shadows[4],
        transform: 'translateY(-1px)',
        transition: 'all 0.2s ease-in-out'
      }
    }}>
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        flexGrow: 1,
        minWidth: 0
      }}>
        <TopicIcon sx={{
          color: 'primary.main',
          fontSize: { xs: 28, sm: 36 },
          flexShrink: 0
        }} />

        <Box sx={{ flexGrow: 1, minWidth: 0 }}>
          <Typography
            variant={isMobile ? "body1" : "subtitle1"}
            sx={{
              fontWeight: 600,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              fontSize: { xs: '1rem', sm: '1.125rem' }
            }}
          >
            {topic.name}
          </Typography>

          <Stack
            direction={isMobile ? "column" : "row"}
            spacing={1}
            alignItems={isMobile ? "flex-start" : "center"}
            flexWrap="wrap"
            mt={0.5}
            sx={{ gap: { xs: 0.5, sm: 1 } }}
          >
            <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
              <Chip
                label={`${topic.partitions} partitions`}
                size="small"
                variant="outlined"
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
              />
              <Chip
                label={`${partitionDetails?.length || 0} replicas`}
                size="small"
                variant="outlined"
                color="secondary"
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
              />
            </Box>
            {messageCount !== undefined ? (
              <Chip
                label={`Messages: ${formatNumber(messageCount)}`}
                size="small"
                variant="outlined"
                color="success"
                icon={<MessageIcon fontSize="small" />}
                sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
              />
            ) : (
              <Button
                size="small"
                variant="outlined"
                startIcon={isLoadingCount ? <CircularProgress size={14} /> : <Refresh />}
                onClick={handleLoadMessageCount}
                sx={{
                  minWidth: 'auto',
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  px: { xs: 1, sm: 2 }
                }}
              >
                {isLoadingCount ? 'Loading...' : 'Load Count'}
              </Button>
            )}
          </Stack>
        </Box>
      </Box>

      <Stack
        direction="row"
        spacing={0.5}
        sx={{
          alignSelf: { xs: 'flex-end', sm: 'center' },
          mt: { xs: 1, sm: 0 }
        }}
      >
        <IconButton
          size="small"
          onClick={() => onView(topic.name)}
          sx={{ p: { xs: 0.5, sm: 1 } }}
        >
          <Visibility fontSize="small" />
        </IconButton>
        <IconButton
          size="small"
          onClick={() => onDelete(topic.name)}
          color="error"
          sx={{ p: { xs: 0.5, sm: 1 } }}
        >
          <Delete fontSize="small" />
        </IconButton>
        <IconButton
          size="small"
          onClick={() => onConfigure(topic.name)}
          sx={{ p: { xs: 0.5, sm: 1 } }}
        >
          <Settings fontSize="small" />
        </IconButton>
      </Stack>
    </Card>
  );
};

const Topics = () => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [topicToDelete, setTopicToDelete] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('sm'));

  // Use the shared topics hook for consistent caching
  const { data: topics, isLoading } = useTopics();

  const filteredTopics = useFilteredTopics(topics?.data || [], searchTerm);


  const createMutation = useMutation(topicsApi.create, {
    onSuccess: () => {
      toast.success('Topic created successfully');
      queryClient.invalidateQueries('topics');
      setCreateDialogOpen(false);
    },
    onError: (error) => {
      toast.error(`Error creating topic: ${error.message}`);
    },
  });

  const deleteMutation = useMutation(topicsApi.delete, {
    onSuccess: () => {
      toast.success('Topic deleted successfully');
      queryClient.invalidateQueries('topics');
      setDeleteConfirmOpen(false);
      setTopicToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting topic: ${error.message}`);
    },
  });

  const handleCreateTopic = (topicData) => {
    createMutation.mutate(topicData);
  };

  const handleDeleteTopic = (topicName) => {
    setTopicToDelete(topicName);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (topicToDelete) {
      deleteMutation.mutate(topicToDelete);
    }
  };

  const handleViewTopic = (topicName) => {
    navigate(`/topics/${topicName}`);
  };

  const handleConfigureTopic = (topicName) => {
    navigate(`/topics/${topicName}`, { state: { activeTab: 3 } });
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between', 
        alignItems: { xs: 'stretch', sm: 'center' }, 
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography 
          variant={isSmallScreen ? "h5" : "h4"}
          sx={{ 
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
          }}
        >
          Topics
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setCreateDialogOpen(true)}
          size={isSmallScreen ? "small" : "medium"}
          fullWidth={isSmallScreen}
        >
          Create Topic
        </Button>
      </Box>

      {/* Performance Notice */}
      {/* <Alert severity="info" sx={{ mb: { xs: 2, sm: 3 } }}>
        <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
          <strong>Performance Note:</strong> Message counts are loaded on-demand to improve page load speed. 
          Click "Load Count" on any topic card to see its message statistics.
        </Typography>
      </Alert> */}

      {/* Search Bar */}
      <Box sx={{ mb: { xs: 2, sm: 3 } }}>
        <TextField
          fullWidth
          placeholder="Search topics..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={() => setSearchTerm('')}
                  edge="end"
                  size="small"
                >
                  <Clear />
                </IconButton>
              </InputAdornment>
            ),
          }}
          variant="outlined"
          size={isSmallScreen ? "small" : "medium"}
        />
      </Box>

      {/* Results Info */}
      {searchTerm && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {filteredTopics.length} topic(s) found for "{searchTerm}"
          </Typography>
        </Box>
      )}

      {topics?.data?.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found. Create your first topic to get started.
        </Alert>
      ) : filteredTopics.length === 0 && searchTerm ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No topics found matching "{searchTerm}". Try a different search term.
        </Alert>
      ) : (
        <List
          height={Math.min(900, window.innerHeight - 300)}
          itemCount={filteredTopics.length}
          itemSize={isMobile ? 160 : 130}
          width="100%"
        >
          {({ index, style }) => {
            const topic = filteredTopics[index];
            return (
              <div style={style} key={topic.name}>
                <TopicCard
                  topic={topic}
                  onView={handleViewTopic}
                  onEdit={() => {}}
                  onDelete={handleDeleteTopic}
                  onConfigure={handleConfigureTopic}
                />
              </div>
            );
          }}
        </List>
      )}

      <CreateTopicDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreateTopic}
      />

      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete topic "{topicToDelete}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button onClick={confirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Topics; 