import React from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Grid,
  Alert,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import { useQuery } from 'react-query';
import { configApi } from '../services/api';

const Settings = () => {
  const { data: config, isLoading } = useQuery('config', configApi.get);
  const { data: kafkaStatus } = useQuery('kafka-status', configApi.getKafkaStatus, {
    refetchInterval: 30000,
  });

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Typography>Loading configuration...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Settings & Configuration
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Kafka Configuration
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Client ID:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.kafka?.clientId || 'N/A'}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Connection Timeout:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.kafka?.connectionTimeout || 0}ms
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Request Timeout:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.kafka?.requestTimeout || 0}ms
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    SSL Enabled:
                  </Typography>
                  <Chip
                    label={config?.data?.kafka?.ssl ? 'Yes' : 'No'}
                    color={config?.data?.kafka?.ssl ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    SASL Mechanism:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.kafka?.sasl?.mechanism || 'None'}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Server Configuration
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Port:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.server?.port || 'N/A'}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Environment:
                  </Typography>
                  <Chip
                    label={config?.data?.server?.nodeEnv || 'Unknown'}
                    color={config?.data?.server?.nodeEnv === 'production' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    CORS Origin:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.server?.corsOrigin || 'N/A'}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Rate Limit Window:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.rateLimiting?.windowMs || 0}ms
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="textSecondary">
                    Rate Limit Max:
                  </Typography>
                  <Typography variant="body2">
                    {config?.data?.rateLimiting?.max || 0} requests
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Kafka Brokers
              </Typography>
              
              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Broker</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Last Check</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {config?.data?.kafka?.brokers?.map((broker, index) => (
                      <TableRow key={index}>
                        <TableCell>{broker}</TableCell>
                        <TableCell>
                          <Chip
                            label={kafkaStatus?.data?.connected ? 'Connected' : 'Disconnected'}
                            color={kafkaStatus?.data?.connected ? 'success' : 'error'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {new Date(kafkaStatus?.data?.timestamp || Date.now()).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Alert severity="info">
            <Typography variant="body1">
              <strong>Note:</strong> This dashboard is configured to work with your Kafka cluster. 
              To modify connection settings, update the environment variables on the server and restart the application.
            </Typography>
          </Alert>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings; 