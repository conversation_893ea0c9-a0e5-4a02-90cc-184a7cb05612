import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Alert,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Notifications,
  Palette,
  Language,
  Security,
  Storage,
  Refresh,
  Save,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { authApi } from '../services/api';
import toast from 'react-hot-toast';

const Settings = () => {
  const [settings, setSettings] = useState({
    notifications: {
      enabled: true,
      email: false,
      desktop: true,
      sound: false,
    },
    display: {
      theme: 'light',
      language: 'en',
      timezone: 'auto',
      refreshInterval: 30,
    },
    dashboard: {
      autoRefresh: true,
      showMetrics: true,
      compactView: false,
    },
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const { data: currentUser } = useQuery('user-profile', authApi.getProfile);

  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }));
  };

  const handleSaveSettings = () => {
    // In a real app, this would save to backend
    localStorage.setItem('userSettings', JSON.stringify(settings));
    toast.success('Settings saved successfully!');
  };

  const handleResetSettings = () => {
    const defaultSettings = {
      notifications: {
        enabled: true,
        email: false,
        desktop: true,
        sound: false,
      },
      display: {
        theme: 'light',
        language: 'en',
        timezone: 'auto',
        refreshInterval: 30,
      },
      dashboard: {
        autoRefresh: true,
        showMetrics: true,
        compactView: false,
      },
    };
    setSettings(defaultSettings);
    localStorage.removeItem('userSettings');
    toast.success('Settings reset to defaults!');
  };

  // Load settings from localStorage on component mount
  React.useEffect(() => {
    const savedSettings = localStorage.getItem('userSettings');
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'stretch', sm: 'center' },
        mb: { xs: 2, sm: 4 },
        gap: { xs: 2, sm: 0 }
      }}>
        <Typography
          variant={isMobile ? "h5" : "h4"}
          sx={{
            fontSize: { xs: '1.5rem', sm: '2.125rem' },
            fontWeight: 600,
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <SettingsIcon /> Application Settings
        </Typography>

        <Box sx={{ display: 'flex', gap: 1, flexDirection: { xs: 'column', sm: 'row' } }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleResetSettings}
            size={isMobile ? "small" : "medium"}
            fullWidth={isMobile}
          >
            Reset to Defaults
          </Button>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSaveSettings}
            size={isMobile ? "small" : "medium"}
            fullWidth={isMobile}
          >
            Save Settings
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* User Profile Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Security /> User Profile
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Username:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {currentUser?.data?.username || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Email:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {currentUser?.data?.email || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Role:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, textTransform: 'capitalize' }}>
                    {currentUser?.data?.role || 'N/A'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="textSecondary">Last Login:</Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {currentUser?.data?.lastLogin ?
                      new Date(currentUser.data.lastLogin).toLocaleString() : 'N/A'}
                  </Typography>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Notifications /> Notification Settings
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="Enable Notifications"
                    secondary="Receive notifications for important events"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.notifications.enabled}
                      onChange={(e) => handleSettingChange('notifications', 'enabled', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Email Notifications"
                    secondary="Send notifications to your email"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.notifications.email}
                      onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                      disabled={!settings.notifications.enabled}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Desktop Notifications"
                    secondary="Show browser notifications"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.notifications.desktop}
                      onChange={(e) => handleSettingChange('notifications', 'desktop', e.target.checked)}
                      disabled={!settings.notifications.enabled}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Sound Alerts"
                    secondary="Play sound for critical alerts"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.notifications.sound}
                      onChange={(e) => handleSettingChange('notifications', 'sound', e.target.checked)}
                      disabled={!settings.notifications.enabled}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Display Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Palette /> Display Settings
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                <FormControl fullWidth>
                  <InputLabel>Theme</InputLabel>
                  <Select
                    value={settings.display.theme}
                    onChange={(e) => handleSettingChange('display', 'theme', e.target.value)}
                    label="Theme"
                  >
                    <MenuItem value="light">Light</MenuItem>
                    <MenuItem value="dark">Dark</MenuItem>
                    <MenuItem value="auto">Auto (System)</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={settings.display.language}
                    onChange={(e) => handleSettingChange('display', 'language', e.target.value)}
                    label="Language"
                  >
                    <MenuItem value="en">English</MenuItem>
                    <MenuItem value="es">Spanish</MenuItem>
                    <MenuItem value="fr">French</MenuItem>
                    <MenuItem value="de">German</MenuItem>
                  </Select>
                </FormControl>

                <FormControl fullWidth>
                  <InputLabel>Refresh Interval (seconds)</InputLabel>
                  <Select
                    value={settings.display.refreshInterval}
                    onChange={(e) => handleSettingChange('display', 'refreshInterval', e.target.value)}
                    label="Refresh Interval (seconds)"
                  >
                    <MenuItem value={10}>10 seconds</MenuItem>
                    <MenuItem value={30}>30 seconds</MenuItem>
                    <MenuItem value={60}>1 minute</MenuItem>
                    <MenuItem value={300}>5 minutes</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Dashboard Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Storage /> Dashboard Settings
              </Typography>
              <List>
                <ListItem>
                  <ListItemText
                    primary="Auto Refresh"
                    secondary="Automatically refresh dashboard data"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.dashboard.autoRefresh}
                      onChange={(e) => handleSettingChange('dashboard', 'autoRefresh', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Show Metrics"
                    secondary="Display detailed metrics on dashboard"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.dashboard.showMetrics}
                      onChange={(e) => handleSettingChange('dashboard', 'showMetrics', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Compact View"
                    secondary="Use compact layout for better space utilization"
                  />
                  <ListItemSecondaryAction>
                    <Switch
                      checked={settings.dashboard.compactView}
                      onChange={(e) => handleSettingChange('dashboard', 'compactView', e.target.checked)}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Information Section */}
        <Grid item xs={12}>
          <Alert severity="info">
            <Typography variant="body1">
              <strong>Note:</strong> Settings are saved locally in your browser.
              For cluster and connection settings, please refer to the <strong>Cluster Info</strong> page.
              Some settings may require a page refresh to take effect.
            </Typography>
          </Alert>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;