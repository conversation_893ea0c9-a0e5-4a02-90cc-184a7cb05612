import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Memory,
  Speed,
  Storage,
  NetworkCheck,
  CheckCircle,
  Error,
  Warning,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { clusterApi } from '../services/api';

const MetricCard = ({ title, value, icon, color = 'primary', subtitle, trend }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box sx={{ flex: 1 }}>
          <Typography color="textSecondary" gutterBottom variant="h6">
            {title}
          </Typography>
          <Typography variant="h4" component="h2" sx={{ mb: 1 }}>
            {value}
          </Typography>
          {subtitle && (
            <Typography variant="body2" color="textSecondary">
              {subtitle}
            </Typography>
          )}
          {trend && (
            <Chip
              label={trend}
              size="small"
              color={trend.includes('+') ? 'success' : 'error'}
              sx={{ mt: 1 }}
            />
          )}
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 60,
            height: 60,
            borderRadius: '50%',
            backgroundColor: `${color}.light`,
            color: `${color}.main`,
          }}
        >
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const ProgressBar = ({ value, label, color = 'primary' }) => (
  <Box sx={{ mb: 2 }}>
    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
      <Typography variant="body2" color="textSecondary">
        {label}
      </Typography>
      <Typography variant="body2" fontWeight="bold">
        {value.toFixed(1)}%
      </Typography>
    </Box>
    <LinearProgress
      variant="determinate"
      value={value}
      sx={{
        height: 8,
        borderRadius: 4,
        backgroundColor: 'grey.200',
        '& .MuiLinearProgress-bar': {
          borderRadius: 4,
          backgroundColor: color === 'error' ? 'error.main' : 
                          color === 'warning' ? 'warning.main' : 
                          color === 'success' ? 'success.main' : 'primary.main'
        }
      }}
    />
  </Box>
);

const Analytics = () => {
  const { data: brokerMetrics, isLoading, error } = useQuery('broker-metrics', clusterApi.getBrokerMetrics, {
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="error">
          Failed to load broker metrics. Please check your connection.
        </Alert>
      </Box>
    );
  }

  const brokers = brokerMetrics?.data?.brokers || [];
  const clusterMetrics = brokerMetrics?.data?.cluster || {};
  const timestamp = brokerMetrics?.data?.timestamp;

  // Helper function to get color based on utilization
  const getUtilizationColor = (value) => {
    if (value >= 80) return 'error';
    if (value >= 60) return 'warning';
    return 'success';
  };

  // Helper function to format bytes
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Broker Analytics
      </Typography>

      {/* Cluster Overview Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Average CPU"
            value={`${clusterMetrics.averageCpu?.toFixed(1) || 0}%`}
            icon={<Speed />}
            color={getUtilizationColor(clusterMetrics.averageCpu || 0)}
            subtitle={`${clusterMetrics.onlineBrokers || 0}/${clusterMetrics.totalBrokers || 0} brokers online`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Average Memory"
            value={`${clusterMetrics.averageMemory?.toFixed(1) || 0}%`}
            icon={<Memory />}
            color={getUtilizationColor(clusterMetrics.averageMemory || 0)}
            subtitle="RAM utilization"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Average Disk"
            value={`${clusterMetrics.averageDisk?.toFixed(1) || 0}%`}
            icon={<Storage />}
            color={getUtilizationColor(clusterMetrics.averageDisk || 0)}
            subtitle="Storage utilization"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Broker Status"
            value={`${clusterMetrics.onlineBrokers || 0}/${clusterMetrics.totalBrokers || 0}`}
            icon={<CheckCircle />}
            color={clusterMetrics.onlineBrokers === clusterMetrics.totalBrokers ? 'success' : 'warning'}
            subtitle="Online brokers"
          />
        </Grid>
      </Grid>

      {/* Broker Details Table */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Broker Details
        </Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Broker ID</TableCell>
                <TableCell>Host:Port</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>CPU</TableCell>
                <TableCell>Memory</TableCell>
                <TableCell>Disk</TableCell>
                <TableCell>Network</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {brokers.map((broker) => (
                <TableRow key={broker.nodeId}>
                  <TableCell>
                    <Typography variant="body2" fontWeight="bold">
                      {broker.nodeId}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {broker.host}:{broker.port}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Rack: {broker.rack}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={broker.status}
                      size="small"
                      color={broker.status === 'online' ? 'success' : 'error'}
                      icon={broker.status === 'online' ? <CheckCircle /> : <Error />}
                    />
                  </TableCell>
                  <TableCell>
                    {broker.isController ? (
                      <Chip
                        label="Controller"
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        Follower
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 100 }}>
                      <ProgressBar
                        value={broker.metrics.cpu.utilization}
                        label="CPU"
                        color={getUtilizationColor(broker.metrics.cpu.utilization)}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 100 }}>
                      <ProgressBar
                        value={broker.metrics.memory.utilization}
                        label="Memory"
                        color={getUtilizationColor(broker.metrics.memory.utilization)}
                      />
                      <Typography variant="caption" color="textSecondary">
                        {formatBytes(broker.metrics.memory.used * 1024 * 1024)} / {formatBytes(broker.metrics.memory.total * 1024 * 1024)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ minWidth: 100 }}>
                      <ProgressBar
                        value={broker.metrics.disk.utilization}
                        label="Disk"
                        color={getUtilizationColor(broker.metrics.disk.utilization)}
                      />
                      <Typography variant="caption" color="textSecondary">
                        {formatBytes(broker.metrics.disk.used * 1024 * 1024)} / {formatBytes(broker.metrics.disk.total * 1024 * 1024)}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontSize="0.75rem">
                        <NetworkCheck sx={{ fontSize: 12, mr: 0.5 }} />
                        {formatBytes(broker.metrics.network.bytesIn)}/s in
                      </Typography>
                      <Typography variant="body2" fontSize="0.75rem">
                        {formatBytes(broker.metrics.network.bytesOut)}/s out
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {broker.metrics.network.requestsPerSec} req/s
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Last Updated */}
      <Box sx={{ textAlign: 'center', mt: 2 }}>
        <Typography variant="caption" color="textSecondary">
          Last updated: {timestamp ? new Date(timestamp).toLocaleString() : 'Unknown'}
        </Typography>
      </Box>
    </Box>
  );
};

export default Analytics; 