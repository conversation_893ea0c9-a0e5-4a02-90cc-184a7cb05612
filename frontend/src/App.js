import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress, useTheme, useMediaQuery } from '@mui/material';
import Navbar from './components/Layout/Navbar';
import Sidebar from './components/Layout/Sidebar';
import ProtectedRoute from './components/ProtectedRoute';
import { EnvironmentProvider } from './contexts/EnvironmentContext';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Topics from './pages/Topics';
import TopicDetail from './pages/TopicDetail';
import ConsumerGroups from './pages/ConsumerGroups';
import ConsumerGroupDetail from './pages/ConsumerGroupDetail';
import MessageBrowser from './pages/MessageBrowser';
import Producer from './pages/Producer';
import ClusterInfo from './pages/ClusterInfo';
import Analytics from './pages/Analytics';
import Settings from './pages/Settings';

const drawerWidth = 240;

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [mobileOpen, setMobileOpen] = useState(false);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Check if user is already logged in on app load
  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
      } catch (error) {
        // Invalid user data, clear storage
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }
    }
    
    setIsLoading(false);
  }, []);

  const handleLogin = (userData, token) => {
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(userData));
  };

  const handleLogout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  };

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: 2,
        }}
      >
        <CircularProgress size={60} />
        <Box sx={{ textAlign: 'center' }}>
          Loading Kafka Dashboard...
        </Box>
      </Box>
    );
  }

  // If not authenticated, show only login route
  if (!isAuthenticated) {
    return (
      <Routes>
        <Route 
          path="/login" 
          element={<Login onLogin={handleLogin} />} 
        />
        <Route 
          path="*" 
          element={<Navigate to="/login" replace />} 
        />
      </Routes>
    );
  }

  // If authenticated, show the main app with navbar and sidebar
  return (
    <EnvironmentProvider>
      <Box sx={{ display: 'flex' }}>
        <Navbar
          user={user}
          onLogout={handleLogout}
          onDrawerToggle={handleDrawerToggle}
          isMobile={isMobile}
        />
        <Sidebar
          drawerWidth={drawerWidth}
          mobileOpen={mobileOpen}
          onDrawerToggle={handleDrawerToggle}
          isMobile={isMobile}
        />
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            p: { xs: 2, sm: 3 },
            width: {
              xs: '100%',
              md: `calc(100% - ${drawerWidth}px)`
            },
            ml: {
              xs: 0,
              md: `${drawerWidth}px`
            },
            mt: { xs: 7, sm: 8 },
            minHeight: '100vh',
          }}
        >
        <Routes>
          <Route 
            path="/" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Dashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/topics" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Topics />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/topics/:topicName" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <TopicDetail />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/consumer-groups" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <ConsumerGroups />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/consumer-groups/:groupId" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <ConsumerGroupDetail />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/messages" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <MessageBrowser />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/producer" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Producer />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/cluster" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <ClusterInfo />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/analytics" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Analytics />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/settings" 
            element={
              <ProtectedRoute isAuthenticated={isAuthenticated}>
                <Settings />
              </ProtectedRoute>
            } 
          />
          {/* Redirect any unknown routes to dashboard */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Box>
    </Box>
    </EnvironmentProvider>
  );
}

export default App; 