{"name": "kafka-dashboard-frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.1", "@mui/material": "^5.14.1", "@mui/x-data-grid": "^6.10.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "axios": "^1.4.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.7.2", "socket.io-client": "^4.7.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.195"}, "proxy": "http://localhost:5000"}