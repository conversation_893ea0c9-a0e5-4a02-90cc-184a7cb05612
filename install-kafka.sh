#!/bin/bash

echo "🚀 Installing Apache Kafka on Ubuntu 20.04..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root. Please run as a regular user with sudo privileges."
   exit 1
fi

# Update system
print_step "Updating System"
sudo apt update && sudo apt upgrade -y

# Install Java
print_step "Installing Java 11"
sudo apt install openjdk-11-jdk -y

# Verify Java installation
if java -version 2>&1 >/dev/null; then
    print_status "Java installed successfully: $(java -version 2>&1 | head -n 1)"
else
    print_error "Java installation failed"
    exit 1
fi

# Set JAVA_HOME
print_step "Setting JAVA_HOME"
JAVA_HOME_PATH="/usr/lib/jvm/java-11-openjdk-amd64"
echo "export JAVA_HOME=$JAVA_HOME_PATH" >> ~/.bashrc
export JAVA_HOME=$JAVA_HOME_PATH

# Create kafka user (optional)
read -p "Do you want to create a dedicated 'kafka' user? (y/n): " create_user
if [[ $create_user == "y" || $create_user == "Y" ]]; then
    if id "kafka" &>/dev/null; then
        print_warning "User 'kafka' already exists"
    else
        sudo useradd kafka -m -s /bin/bash
        sudo usermod -aG sudo kafka
        print_status "User 'kafka' created"
    fi
fi

# Download and install Kafka
print_step "Downloading and Installing Kafka"
KAFKA_VERSION="2.13-3.5.0"
KAFKA_URL="https://downloads.apache.org/kafka/2.13-3.5.0/kafka_${KAFKA_VERSION}.tgz"

# Create temporary download directory
mkdir -p ~/kafka-install
cd ~/kafka-install

# Download Kafka
print_status "Downloading Kafka..."
wget -q --show-progress $KAFKA_URL

if [ $? -eq 0 ]; then
    print_status "Kafka downloaded successfully"
else
    print_error "Failed to download Kafka"
    exit 1
fi

# Extract Kafka
print_status "Extracting Kafka..."
tar -xzf kafka_${KAFKA_VERSION}.tgz

# Move to /opt
sudo mv kafka_${KAFKA_VERSION} /opt/kafka
sudo chown -R $USER:$USER /opt/kafka

# Create symlink
sudo ln -sf /opt/kafka /usr/local/kafka

# Add to PATH
echo 'export PATH="$PATH:/opt/kafka/bin"' >> ~/.bashrc

# Create log directories
print_step "Creating Log Directories"
sudo mkdir -p /var/log/kafka
sudo mkdir -p /var/log/zookeeper
sudo chown -R $USER:$USER /var/log/kafka
sudo chown -R $USER:$USER /var/log/zookeeper

# Configure Kafka
print_step "Configuring Kafka"

# Backup original config
sudo cp /opt/kafka/config/server.properties /opt/kafka/config/server.properties.backup

# Update server.properties
cat > /tmp/server.properties << 'EOF'
broker.id=0
listeners=PLAINTEXT://localhost:9092
advertised.listeners=PLAINTEXT://localhost:9092
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
log.dirs=/var/log/kafka
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=1
transaction.state.log.replication.factor=1
transaction.state.log.min.isr=1
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=localhost:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
delete.topic.enable=true
auto.create.topics.enable=true
EOF

sudo cp /tmp/server.properties /opt/kafka/config/server.properties

# Update zookeeper.properties
cat > /tmp/zookeeper.properties << 'EOF'
dataDir=/var/log/zookeeper
clientPort=2181
maxClientCnxns=0
tickTime=2000
initLimit=5
syncLimit=2
admin.enableServer=false
EOF

sudo cp /tmp/zookeeper.properties /opt/kafka/config/zookeeper.properties

# Create systemd services
print_step "Creating Systemd Services"

# Zookeeper service
sudo tee /etc/systemd/system/zookeeper.service > /dev/null << EOF
[Unit]
Description=Apache Zookeeper server
Documentation=http://zookeeper.apache.org
Requires=network.target remote-fs.target
After=network.target remote-fs.target

[Service]
Type=simple
ExecStart=/opt/kafka/bin/zookeeper-server-start.sh /opt/kafka/config/zookeeper.properties
ExecStop=/opt/kafka/bin/zookeeper-server-stop.sh
Restart=on-abnormal
User=$USER
Group=$USER
Environment="JAVA_HOME=$JAVA_HOME_PATH"

[Install]
WantedBy=multi-user.target
EOF

# Kafka service
sudo tee /etc/systemd/system/kafka.service > /dev/null << EOF
[Unit]
Description=Apache Kafka Server
Documentation=http://kafka.apache.org/documentation.html
Requires=zookeeper.service
After=zookeeper.service

[Service]
Type=simple
ExecStart=/opt/kafka/bin/kafka-server-start.sh /opt/kafka/config/server.properties
ExecStop=/opt/kafka/bin/kafka-server-stop.sh
Restart=on-abnormal
User=$USER
Group=$USER
Environment="JAVA_HOME=$JAVA_HOME_PATH"

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable services
sudo systemctl daemon-reload
sudo systemctl enable zookeeper
sudo systemctl enable kafka

print_status "Systemd services created and enabled"

# Start services
print_step "Starting Kafka Services"
print_status "Starting Zookeeper..."
sudo systemctl start zookeeper

# Wait for Zookeeper to start
sleep 10

print_status "Starting Kafka..."
sudo systemctl start kafka

# Wait for Kafka to start
sleep 15

# Check service status
print_step "Checking Service Status"
if sudo systemctl is-active --quiet zookeeper; then
    print_status "Zookeeper is running ✓"
else
    print_error "Zookeeper failed to start"
    sudo systemctl status zookeeper
fi

if sudo systemctl is-active --quiet kafka; then
    print_status "Kafka is running ✓"
else
    print_error "Kafka failed to start"
    sudo systemctl status kafka
fi

# Test installation
print_step "Testing Kafka Installation"
source ~/.bashrc
export PATH="$PATH:/opt/kafka/bin"

# Create test topic
print_status "Creating test topic..."
/opt/kafka/bin/kafka-topics.sh --create --topic test-topic --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists

# List topics
print_status "Listing topics..."
/opt/kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092

# Cleanup
rm -rf ~/kafka-install

print_step "Installation Complete! 🎉"
echo ""
echo -e "${GREEN}Kafka has been successfully installed and configured!${NC}"
echo ""
echo -e "${BLUE}Useful Commands:${NC}"
echo "  sudo systemctl start kafka     - Start Kafka"
echo "  sudo systemctl stop kafka      - Stop Kafka"
echo "  sudo systemctl status kafka    - Check Kafka status"
echo "  sudo systemctl restart kafka   - Restart Kafka"
echo ""
echo -e "${BLUE}Kafka is running on:${NC} localhost:9092"
echo -e "${BLUE}Zookeeper is running on:${NC} localhost:2181"
echo ""
echo -e "${BLUE}Test your installation:${NC}"
echo "  /opt/kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092"
echo ""
echo -e "${BLUE}View logs:${NC}"
echo "  sudo journalctl -u kafka -f"
echo "  sudo journalctl -u zookeeper -f"
echo ""
echo -e "${GREEN}Now you can start your Kafka Dashboard!${NC}"
echo "  cd /path/to/kafka-dashboard"
echo "  npm run dev" 