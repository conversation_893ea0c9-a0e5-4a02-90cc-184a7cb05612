const { Kafka, CompressionTypes, CompressionCodecs } = require('kafkajs');
const SnappyCodec = require('kafkajs-snappy');
const environmentService = require('../services/environmentService');
const logger = require('../utils/logger');

// Register Snappy compression codec
CompressionCodecs[CompressionTypes.Snappy] = SnappyCodec;

class DynamicKafkaClient {
  constructor() {
    this.clients = new Map(); // Store clients for different environments
    this.currentEnvironment = null;
    this.currentClient = null;
  }

  /**
   * Get or create Kafka client for specific environment
   */
  getKafkaClient(environment = null) {
    const env = environment || environmentService.getCurrentEnvironment().key;
    
    if (!this.clients.has(env)) {
      const kafkaConfig = environmentService.getKafkaConfig(env);
      
      const kafka = new Kafka({
        clientId: kafkaConfig.clientId,
        brokers: kafkaConfig.brokers,
        connectionTimeout: kafkaConfig.connectionTimeout,
        requestTimeout: kafkaConfig.requestTimeout,
        ssl: kafkaConfig.ssl,
        sasl: kafkaConfig.sasl
      });
      
      this.clients.set(env, {
        kafka,
        admin: kafka.admin(),
        isConnected: false,
        consumers: new Map(),
        producers: new Map(),
        isConnecting: false
      });
      
      logger.info(`Created Kafka client for environment: ${env}`);
    }
    
    return this.clients.get(env);
  }

  /**
   * Get current active client
   */
  getCurrentClient() {
    const currentEnv = environmentService.getCurrentEnvironment().key;
    
    if (this.currentEnvironment !== currentEnv) {
      this.currentEnvironment = currentEnv;
      this.currentClient = this.getKafkaClient(currentEnv);
    }
    
    return this.currentClient;
  }

  /**
   * Connect to current environment
   */
  async connect() {
    const client = this.getCurrentClient();
    
    if (client.isConnected || client.isConnecting) {
      return;
    }

    try {
      client.isConnecting = true;
      await client.admin.connect();
      client.isConnected = true;
      client.isConnecting = false;
      
      const env = environmentService.getCurrentEnvironment();
      logger.info(`Connected to Kafka - Environment: ${env.name}`);
    } catch (error) {
      client.isConnecting = false;
      logger.error('Failed to connect to Kafka:', error);
      throw error;
    }
  }

  /**
   * Disconnect from current environment
   */
  async disconnect() {
    const client = this.getCurrentClient();
    
    if (!client.isConnected) {
      return;
    }

    try {
      // Disconnect all consumers
      for (const [key, consumer] of client.consumers) {
        try {
          await consumer.disconnect();
        } catch (error) {
          logger.warn(`Error disconnecting consumer ${key}:`, error);
        }
      }
      client.consumers.clear();

      // Disconnect all producers
      for (const [key, producer] of client.producers) {
        try {
          await producer.disconnect();
        } catch (error) {
          logger.warn(`Error disconnecting producer ${key}:`, error);
        }
      }
      client.producers.clear();

      // Disconnect admin
      await client.admin.disconnect();
      client.isConnected = false;
      
      logger.info('Disconnected from Kafka');
    } catch (error) {
      logger.error('Error during Kafka disconnect:', error);
      throw error;
    }
  }

  /**
   * Switch environment and reconnect
   */
  async switchEnvironment(environment) {
    try {
      // Disconnect from current environment
      if (this.currentClient && this.currentClient.isConnected) {
        await this.disconnect();
      }

      // Switch environment in service
      environmentService.switchEnvironment(environment);
      
      // Connect to new environment
      await this.connect();
      
      const env = environmentService.getCurrentEnvironment();
      logger.info(`Successfully switched to environment: ${env.name}`);
      
      return env;
    } catch (error) {
      logger.error('Error switching environment:', error);
      throw error;
    }
  }

  /**
   * Ensure connected to current environment
   */
  async ensureConnected() {
    const client = this.getCurrentClient();
    
    if (!client.isConnected && !client.isConnecting) {
      try {
        await this.connect();
      } catch (error) {
        logger.warn('Auto-reconnect failed:', error);
        throw error;
      }
    }
  }

  /**
   * Get admin client for current environment
   */
  getAdmin() {
    return this.getCurrentClient().admin;
  }

  /**
   * Get or create consumer for current environment
   */
  getConsumer(groupId, options = {}) {
    const client = this.getCurrentClient();
    const key = `${groupId}-${JSON.stringify(options)}`;
    
    if (!client.consumers.has(key)) {
      const consumer = client.kafka.consumer({
        groupId,
        ...options
      });
      client.consumers.set(key, consumer);
    }
    
    return client.consumers.get(key);
  }

  /**
   * Get or create producer for current environment
   */
  getProducer(options = {}) {
    const client = this.getCurrentClient();
    const key = JSON.stringify(options);
    
    if (!client.producers.has(key)) {
      const producer = client.kafka.producer(options);
      client.producers.set(key, producer);
    }
    
    return client.producers.get(key);
  }

  /**
   * Get current environment info
   */
  getCurrentEnvironmentInfo() {
    return environmentService.getCurrentEnvironment();
  }

  /**
   * Clean up all clients
   */
  async cleanup() {
    for (const [env, client] of this.clients) {
      try {
        if (client.isConnected) {
          // Disconnect consumers
          for (const consumer of client.consumers.values()) {
            await consumer.disconnect();
          }
          
          // Disconnect producers
          for (const producer of client.producers.values()) {
            await producer.disconnect();
          }
          
          // Disconnect admin
          await client.admin.disconnect();
        }
      } catch (error) {
        logger.warn(`Error cleaning up client for environment ${env}:`, error);
      }
    }
    
    this.clients.clear();
    this.currentClient = null;
    this.currentEnvironment = null;
  }
}

// Create singleton instance
const dynamicKafkaClient = new DynamicKafkaClient();

module.exports = dynamicKafkaClient;
