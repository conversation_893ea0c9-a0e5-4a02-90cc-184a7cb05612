const express = require('express');
const kafkaClient = require('../kafka/kafkaClient');
const dynamicKafkaClient = require('../kafka/dynamicKafkaClient');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// GET /api/cluster/info - Get cluster information
router.get('/info', async (req, res) => {
  try {
    const clusterInfo = await kafkaClient.getClusterInfo();
    res.json({
      success: true,
      data: clusterInfo
    });
  } catch (error) {
    logger.error('Error fetching cluster info:', error);

    // Return a default response instead of error
    res.json({
      success: false,
      data: {
        brokers: [],
        topics: 0,
        clusterId: 'unavailable'
      },
      error: 'Cluster information unavailable'
    });
  }
});

// GET /api/cluster/info-with-env - Get cluster information with environment context
router.get('/info-with-env', authenticateToken, async (req, res) => {
  try {
    await dynamicKafkaClient.ensureConnected();
    const admin = dynamicKafkaClient.getAdmin();

    // Get cluster metadata
    const metadata = await admin.fetchTopicMetadata();
    const brokers = await admin.describeCluster();

    // Get environment info
    const environmentInfo = dynamicKafkaClient.getCurrentEnvironmentInfo();

    const clusterInfo = {
      clusterId: brokers.clusterId || 'unknown',
      brokers: brokers.brokers.map(broker => ({
        nodeId: broker.nodeId,
        host: broker.host,
        port: broker.port,
        rack: broker.rack
      })),
      topics: metadata.topics.length,
      environment: environmentInfo,
      connectionTime: new Date().toISOString()
    };

    res.json({
      success: true,
      data: clusterInfo
    });
  } catch (error) {
    logger.error('Error fetching cluster info with environment:', error);

    const environmentInfo = dynamicKafkaClient.getCurrentEnvironmentInfo();

    res.json({
      success: false,
      data: {
        brokers: [],
        topics: 0,
        clusterId: 'unavailable',
        environment: environmentInfo,
        error: error.message
      },
      error: 'Cluster information unavailable'
    });
  }
});

// GET /api/cluster/health - Get cluster health status
router.get('/health', async (req, res) => {
  try {
    const isHealthy = kafkaClient.isHealthy();
    
    if (isHealthy) {
      const clusterInfo = await kafkaClient.getClusterInfo();
      
      res.json({
        success: true,
        data: {
          status: 'healthy',
          connected: true,
          brokers: clusterInfo.brokers || [],
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          status: 'unhealthy',
          connected: false,
          brokers: [],
          timestamp: new Date().toISOString()
        }
      });
    }
  } catch (error) {
    logger.error('Error checking cluster health:', error);
    
    // Always return a response, even on error
    res.json({
      success: true,
      data: {
        status: 'unhealthy',
        connected: false,
        brokers: [],
        timestamp: new Date().toISOString(),
        error: 'Health check failed'
      }
    });
  }
});

// GET /api/cluster/message-flow - Get message flow data for dashboard (Not using currently)
router.get('/message-flow', async (req, res) => {
  try {
    const messageFlowData = await kafkaClient.getMessageFlowData();
    res.json({
      success: true,
      data: messageFlowData
    });
  } catch (error) {
    logger.error('Error fetching message flow data:', error);
    res.json({
      success: false,
      data: {
        messageFlowData: [],
        totalMessages: 0,
        topicsCount: 0,
        topTopics: []
      },
      error: 'Message flow data unavailable'
    });
  }
});

// GET /api/cluster/broker-metrics - Get broker metrics including CPU and Memory
router.get('/broker-metrics', async (req, res) => {
  try {
    const brokerMetrics = await kafkaClient.getBrokerMetrics();
    res.json({
      success: true,
      data: brokerMetrics
    });
  } catch (error) {
    logger.error('Error fetching broker metrics:', error);
    res.json({
      success: false,
      data: {
        brokers: [],
        cluster: {
          averageCpu: 0,
          averageMemory: 0,
          averageDisk: 0,
          totalBrokers: 0,
          onlineBrokers: 0,
          controllerBroker: null
        },
        timestamp: new Date().toISOString()
      },
      error: 'Broker metrics unavailable'
    });
  }
});

// GET /api/cluster/message-rate - Get real-time message rate
router.get('/message-rate', async (req, res) => {
  try {
    const messageRate = await kafkaClient.getMessageRate();
    res.json({
      success: true,
      data: messageRate
    });
  } catch (error) {
    logger.error('Error fetching message rate:', error);
    res.json({
      success: false,
      data: {
        messagesPerSecond: 0,
        totalMessages: 0,
        topicsCount: 0,
        timestamp: new Date().toISOString()
      },
      error: 'Message rate data unavailable'
    });
  }
});

module.exports = router; 