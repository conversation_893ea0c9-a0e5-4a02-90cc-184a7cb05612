#!/bin/bash

# Kafka Dashboard Deployment Script
# Usage: ./deploy.sh [local|qa|prod] [start|stop|restart|status]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="local"
ACTION="start"
BACKEND_PORT=5000
FRONTEND_PORT=3000

# Parse arguments
if [ $# -ge 1 ]; then
    ENVIRONMENT=$1
fi

if [ $# -ge 2 ]; then
    ACTION=$2
fi

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(local|qa|prod)$ ]]; then
    echo -e "${RED}Error: Invalid environment '$ENVIRONMENT'. Must be 'local', 'qa', or 'prod'${NC}"
    exit 1
fi

# Validate action
if [[ ! "$ACTION" =~ ^(start|stop|restart|status)$ ]]; then
    echo -e "${RED}Error: Invalid action '$ACTION'. Must be 'start', 'stop', 'restart', or 'status'${NC}"
    exit 1
fi

# Function to print header
print_header() {
    echo -e "${BLUE}=================================================${NC}"
    echo -e "${BLUE}  Kafka Dashboard - $ENVIRONMENT Environment${NC}"
    echo -e "${BLUE}=================================================${NC}"
}

# Function to check if process is running
is_running() {
    local port=$1
    lsof -i :$port > /dev/null 2>&1
}

# Function to get process PID
get_pid() {
    local port=$1
    lsof -ti :$port 2>/dev/null
}

# Function to kill process
kill_process() {
    local port=$1
    local name=$2
    
    if is_running $port; then
        local pid=$(get_pid $port)
        echo -e "${YELLOW}Stopping $name (PID: $pid)...${NC}"
        kill $pid
        sleep 2
        
        if is_running $port; then
            echo -e "${YELLOW}Force stopping $name...${NC}"
            kill -9 $pid
        fi
        echo -e "${GREEN}$name stopped${NC}"
    else
        echo -e "${YELLOW}$name is not running${NC}"
    fi
}

# Function to start services
start_services() {
    print_header
    echo -e "${GREEN}Starting services in $ENVIRONMENT environment...${NC}"
    
    # Install dependencies if not already installed
    if [ ! -d "node_modules" ] || [ ! -d "backend/node_modules" ] || [ ! -d "frontend/node_modules" ]; then
        echo -e "${YELLOW}Installing dependencies...${NC}"
        npm run install-deps
    fi
    
    # Start based on environment
    case $ENVIRONMENT in
        local)
            echo -e "${GREEN}Starting local development environment...${NC}"
            npm run start:local
            ;;
        qa)
            echo -e "${GREEN}Starting QA environment...${NC}"
            npm run start:qa
            ;;
        prod)
            echo -e "${GREEN}Starting production environment...${NC}"
            if [ "$ACTION" = "start" ]; then
                echo -e "${YELLOW}Building frontend for production...${NC}"
                cd frontend && npm run build && cd ..
                echo -e "${GREEN}Starting backend in production mode...${NC}"
                npm run prod:backend &
                echo -e "${GREEN}Starting frontend server...${NC}"
                npm run prod:frontend &
            else
                npm run start:prod
            fi
            ;;
    esac
}

# Function to stop services
stop_services() {
    print_header
    echo -e "${RED}Stopping services...${NC}"
    
    kill_process $BACKEND_PORT "Backend"
    kill_process $FRONTEND_PORT "Frontend"
    
    # Kill any remaining npm processes
    pkill -f "npm.*run.*" 2>/dev/null || true
    pkill -f "node.*server.js" 2>/dev/null || true
    pkill -f "react-scripts" 2>/dev/null || true
    
    echo -e "${GREEN}All services stopped${NC}"
}

# Function to show status
show_status() {
    print_header
    echo -e "${BLUE}Service Status:${NC}"
    
    if is_running $BACKEND_PORT; then
        echo -e "${GREEN}✓ Backend (port $BACKEND_PORT): Running${NC}"
    else
        echo -e "${RED}✗ Backend (port $BACKEND_PORT): Not running${NC}"
    fi
    
    if is_running $FRONTEND_PORT; then
        echo -e "${GREEN}✓ Frontend (port $FRONTEND_PORT): Running${NC}"
    else
        echo -e "${RED}✗ Frontend (port $FRONTEND_PORT): Not running${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}Environment Configuration:${NC}"
    echo -e "Environment: ${YELLOW}$ENVIRONMENT${NC}"
    
    case $ENVIRONMENT in
        local)
            echo -e "Kafka Brokers: ${YELLOW}localhost:9092${NC}"
            echo -e "Authentication: ${YELLOW}None${NC}"
            ;;
        qa)
            echo -e "Kafka Brokers: ${YELLOW}***********:9092${NC}"
            echo -e "Authentication: ${YELLOW}SASL/PLAIN${NC}"
            echo -e "Username: ${YELLOW}bmskfk${NC}"
            ;;
        prod)
            echo -e "Kafka Brokers: ${YELLOW}***********:9092,************:9092,************:9092${NC}"
            echo -e "Authentication: ${YELLOW}SASL/PLAIN${NC}"
            echo -e "Username: ${YELLOW}bmskfk${NC}"
            ;;
    esac
    
    echo ""
    echo -e "${BLUE}Access URLs:${NC}"
    echo -e "Frontend: ${YELLOW}http://localhost:$FRONTEND_PORT${NC}"
    echo -e "Backend API: ${YELLOW}http://localhost:$BACKEND_PORT${NC}"
    echo -e "Health Check: ${YELLOW}http://localhost:$BACKEND_PORT/health${NC}"
}

# Function to restart services
restart_services() {
    stop_services
    sleep 2
    start_services
}

# Main execution
case $ACTION in
    start)
        start_services
        ;;
    stop)
        stop_services
        ;;
    restart)
        restart_services
        ;;
    status)
        show_status
        ;;
esac

echo ""
echo -e "${GREEN}Operation completed successfully!${NC}"

# Show helpful information
if [ "$ACTION" = "start" ]; then
    echo ""
    echo -e "${BLUE}Helpful Commands:${NC}"
    echo -e "  Check status: ${YELLOW}./deploy.sh $ENVIRONMENT status${NC}"
    echo -e "  Stop services: ${YELLOW}./deploy.sh $ENVIRONMENT stop${NC}"
    echo -e "  Restart services: ${YELLOW}./deploy.sh $ENVIRONMENT restart${NC}"
    echo ""
    echo -e "${BLUE}Access the application at:${NC}"
    echo -e "  Frontend: ${YELLOW}http://localhost:$FRONTEND_PORT${NC}"
    echo -e "  Backend API: ${YELLOW}http://localhost:$BACKEND_PORT${NC}"
    echo ""
    echo -e "${BLUE}To view logs:${NC}"
    echo -e "  Backend logs: ${YELLOW}tail -f backend/logs/combined.log${NC}"
    echo -e "  Error logs: ${YELLOW}tail -f backend/logs/error.log${NC}"
fi 