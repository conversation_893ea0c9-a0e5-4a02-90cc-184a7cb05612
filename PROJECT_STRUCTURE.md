# Project Structure

```
kafka-dashboard/
├── package.json              # Root package.json with scripts
├── README.md                  # Comprehensive documentation
├── setup.sh                  # Quick setup script
├── PROJECT_STRUCTURE.md      # This file
│
├── backend/                   # Node.js Backend
│   ├── package.json          # Backend dependencies
│   ├── server.js             # Main server entry point
│   ├── config/
│   │   └── config.js         # Configuration management
│   ├── kafka/
│   │   └── kafkaClient.js    # Kafka client wrapper
│   ├── routes/
│   │   ├── topics.js         # Topic management routes
│   │   ├── consumers.js      # Consumer group routes
│   │   ├── producers.js      # Producer routes
│   │   ├── cluster.js        # Cluster info routes
│   │   └── config.js         # Configuration routes
│   ├── utils/
│   │   └── logger.js         # Winston logger setup
│   └── logs/                 # Application logs
│
└── frontend/                  # React.js Frontend
    ├── package.json          # Frontend dependencies
    ├── public/
    │   └── index.html        # Main HTML template
    └── src/
        ├── index.js          # React app entry point
        ├── App.js            # Main app component
        ├── components/
        │   └── Layout/
        │       ├── Navbar.js # Top navigation
        │       └── Sidebar.js # Side navigation
        ├── pages/
        │   ├── Dashboard.js           # Main dashboard
        │   ├── Topics.js              # Topics management
        │   ├── TopicDetail.js         # Individual topic view
        │   ├── ConsumerGroups.js      # Consumer groups list
        │   ├── ConsumerGroupDetail.js # Consumer group details
        │   ├── MessageBrowser.js      # Message browsing
        │   ├── Producer.js            # Message producer
        │   ├── ClusterInfo.js         # Cluster information
        │   └── Settings.js            # Application settings
        └── services/
            ├── api.js        # API client configuration
            └── socket.js     # WebSocket client
```

## Key Features by File

### Backend Components

#### `server.js`
- Express.js server setup
- Socket.IO integration  
- Route mounting
- Error handling
- Graceful shutdown

#### `kafka/kafkaClient.js`
- Kafka connection management
- Topic CRUD operations
- Consumer group management
- Message production/consumption
- Real-time streaming support

#### `routes/topics.js`
- Topic listing and creation
- Topic deletion and configuration
- Partition management
- Message browsing and production
- Real-time subscription management

#### `routes/consumers.js`
- Consumer group listing
- Group details and member info
- Consumer group deletion

#### `routes/cluster.js`
- Cluster health monitoring
- Broker information
- Connection status

### Frontend Components

#### `pages/Dashboard.js`
- Overview metrics and charts
- Cluster health status
- Quick statistics
- Real-time updates

#### `pages/Topics.js`
- Topic listing with cards
- Topic creation dialog
- Topic deletion confirmation
- Search and filtering

#### `pages/TopicDetail.js`
- Topic information tabs
- Message browsing with real-time
- Partition management
- Configuration editing

#### `pages/Producer.js`
- Message composition form
- Message history tracking
- Bulk message sending
- Header management

#### `pages/MessageBrowser.js`
- Advanced message search
- Pagination and filtering
- Message export functionality
- Multi-topic browsing

#### `pages/ConsumerGroups.js`
- Consumer group listing
- Group state monitoring
- Member management
- Offset tracking

### Services

#### `services/api.js`
- Axios configuration
- API endpoint definitions
- Request/response interceptors
- Error handling

#### `services/socket.js`
- Socket.IO client
- Real-time message subscription
- Connection management
- Event handling

## Data Flow

1. **Frontend → Backend**: REST API calls via axios
2. **Backend → Kafka**: KafkaJS client operations
3. **Real-time Updates**: Socket.IO WebSocket connection
4. **State Management**: React Query for caching and synchronization

## Configuration Files

- `backend/.env` - Environment variables
- `backend/config/config.js` - Application configuration
- `frontend/package.json` - Proxy configuration for development

## Build & Deployment

- **Development**: `npm run dev` (concurrent backend + frontend)
- **Production**: `npm run build` + `npm run server`
- **Backend Only**: `npm run server`
- **Frontend Only**: `npm run client`

## Security Features

- CORS protection
- Rate limiting
- Input validation (Joi)
- Error sanitization
- SSL/SASL support for Kafka

## Monitoring & Logging

- Winston logging framework
- Request/response logging
- Error tracking
- Health check endpoints
- Real-time connection monitoring 