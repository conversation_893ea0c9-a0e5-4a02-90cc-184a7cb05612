# Kafka Dashboard - PolicyBazaar

A comprehensive web-based dashboard for managing and monitoring Apache Kafka clusters. Built with React.js frontend and Node.js backend, this dashboard provides an intuitive interface for Kafka administration tasks.

## 🚀 Features

### ✅ Core Functionality
- **Topic Management**: Create, view, modify, and delete Kafka topics
- **Partition Management**: Add partitions to existing topics
- **Consumer Group Monitoring**: View and manage consumer groups
- **Message Browser**: Search and view messages across topics and partitions
- **Message Producer**: Send messages to Kafka topics with custom headers
- **Real-time Monitoring**: Live message streaming via WebSocket
- **Cluster Information**: Monitor broker health and cluster status

### 🎯 Key Capabilities
- **Dashboard Overview**: Real-time metrics and cluster health monitoring
- **Topic Operations**: 
  - Create topics with custom partition count and replication factor
  - View topic details including partition distribution
  - Delete topics with confirmation
  - Add partitions to existing topics
- **Message Operations**:
  - Browse messages by topic, partition, and offset
  - Real-time message streaming
  - Export messages to JSON
  - Send custom messages with headers
- **Consumer Group Management**:
  - View all consumer groups with their states
  - Monitor group members and their assignments
  - View offset information per topic/partition
  - Delete inactive consumer groups
- **Monitoring & Analytics**:
  - Cluster health status
  - Broker information and connectivity
  - Message flow visualization
  - Real-time updates via WebSocket

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React.js)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │  Dashboard  │ │   Topics    │ │  Consumer Groups   │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │  Producer   │ │   Browser   │ │    Cluster Info    │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/WebSocket
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Backend (Node.js)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │ Express.js  │ │  Socket.IO  │ │    Kafka Client     │    │
│  │   Server    │ │   Server    │ │     (kafkajs)       │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                         Kafka Protocol
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Kafka Cluster                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │   Broker 1  │ │   Broker 2  │ │      Broker 3      │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Prerequisites

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **Apache Kafka** cluster (running and accessible)
- **Zookeeper** (if using Kafka versions < 2.8)

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd kafka-dashboard
```

### 2. Install Dependencies
```bash
# Install all dependencies (root, backend, and frontend)
npm run install
```

### 3. Environment Configuration

The application now supports **dynamic environment switching** through the UI! No need for separate environment configurations.

#### Environment Overview
- **Local**: Development environment with local Kafka (default)
- **QA**: Quality Assurance environment with authentication
- **Production**: Production environment with multiple brokers

#### Environment-Specific Configuration

| Environment | Kafka Brokers | Authentication | SSL |
|-------------|---------------|----------------|-----|
| **Local** | localhost:9092 | None | Disabled |
| **QA** | ***********:9092 | SASL/PLAIN | Disabled |
| **Production** | ***********:9092,<br>************:9092,<br>************:9092 | SASL/PLAIN | Disabled |

#### Optional Environment Variables Override

Create a `.env` file in the `backend` directory to override default settings:

```bash
# backend/.env

# Server Configuration
PORT=5000
NODE_ENV=local  # local, qa, or prod

# Kafka Configuration (optional overrides)
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=kafka-dashboard
KAFKA_GROUP_ID=kafka-dashboard-group

# For multiple brokers, use comma-separated values:
# KAFKA_BROKERS=broker1:9092,broker2:9092,broker3:9092

# Optional: SSL Configuration
# KAFKA_SSL=true

# Optional: SASL Authentication
# KAFKA_SASL_MECHANISM=PLAIN
# KAFKA_SASL_USERNAME=your-username
# KAFKA_SASL_PASSWORD=your-password

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
```

### 4. Start the Application

#### Development Mode (Recommended)
```bash
# Start both backend and frontend in development mode
npm run dev

# The application will be available at:
# Frontend: http://localhost:3000
# Backend API: http://localhost:5000
```

#### Production Mode
```bash
# Build frontend for production
npm run build

# Start both backend and frontend in production mode
npm run start
```

#### Individual Components
```bash
# Backend only (development)
cd backend && npm run dev

# Backend only (production)
cd backend && npm start

# Frontend only
cd frontend && npm start
```

### 5. Environment Switching

🎉 **New Feature**: Dynamic environment switching without restarting the application!

1. **Start the application** using `npm run dev`
2. **Login** to the dashboard
3. **Use the environment selector** in the navbar to switch between:
   - **QA Environment**: Single Kafka broker
   - **Production Environment**: Multiple Kafka brokers
4. **All data updates automatically** - topics, consumers, messages are environment-specific

#### Supported Environments
- **QA**: `***********:9092` (1 broker)
- **Production**: `***********:9092`, `************:9092`, `************:9092` (3 brokers)

##### Traditional Development Mode
```bash
# Start both backend and frontend in development mode
npm run dev

# Start backend only
npm run server

# Start frontend only
npm run client
```

##### Production Mode
```bash
# Build the frontend
npm run build

# Start production backend
npm run prod:backend

# Start production frontend
npm run prod:frontend
```

## 🚀 Deployment Script

The `deploy.sh` script provides a convenient way to manage the application across different environments.

### Usage
```bash
./deploy.sh [environment] [action]
```

### Environments
- `local` - Development environment (default)
- `qa` - Quality Assurance environment
- `prod` - Production environment

### Actions
- `start` - Start the application (default)
- `stop` - Stop all services
- `restart` - Restart all services
- `status` - Show service status and configuration

### Examples
```bash
# Start QA environment
./deploy.sh qa start

# Check production status
./deploy.sh prod status

# Restart local environment
./deploy.sh local restart

# Stop all services in QA
./deploy.sh qa stop
```

### Features
- **Automatic Dependency Installation**: Installs dependencies if not present
- **Process Management**: Manages backend and frontend processes
- **Environment Validation**: Validates environment and action parameters
- **Status Monitoring**: Shows service status and environment configuration
- **Graceful Shutdown**: Properly stops all related processes
- **Colored Output**: Easy-to-read colored terminal output

## 🔧 Configuration Options

### Backend Configuration (`backend/config/config.js`)

| Option | Description | Default |
|--------|-------------|---------|
| `KAFKA_BROKERS` | Comma-separated list of Kafka brokers | `localhost:9092` |
| `KAFKA_CLIENT_ID` | Client ID for Kafka connections | `kafka-dashboard` |
| `KAFKA_GROUP_ID` | Consumer group ID | `kafka-dashboard-group` |
| `KAFKA_SSL` | Enable SSL for Kafka connections | `false` |
| `KAFKA_SASL_MECHANISM` | SASL mechanism (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512) | `null` |
| `PORT` | Backend server port | `5000` |
| `CORS_ORIGIN` | Allowed CORS origin | `http://localhost:3000` |

### Frontend Configuration

The frontend automatically proxies API requests to the backend. If you need to change the backend URL, update the `proxy` field in `frontend/package.json`.

## 📚 API Documentation

### Topics API
- `GET /api/topics` - List all topics
- `POST /api/topics` - Create a new topic
- `GET /api/topics/:topicName` - Get topic details
- `PUT /api/topics/:topicName` - Update topic configuration
- `DELETE /api/topics/:topicName` - Delete a topic
- `POST /api/topics/:topicName/partitions` - Add partitions
- `GET /api/topics/:topicName/messages` - Get messages
- `POST /api/topics/:topicName/messages` - Produce a message

### Consumer Groups API
- `GET /api/consumers` - List all consumer groups
- `GET /api/consumers/:groupId` - Get consumer group details
- `DELETE /api/consumers/:groupId` - Delete consumer group

### Cluster API
- `GET /api/cluster/info` - Get cluster information
- `GET /api/cluster/health` - Get cluster health status

### Configuration API
- `GET /api/config` - Get application configuration
- `GET /api/config/kafka-status` - Get Kafka connection status

## 🎯 Usage Guide

### 1. Dashboard Overview
- View cluster health and connection status
- Monitor topic and consumer group counts
- See real-time message flow charts
- Check broker information

### 2. Managing Topics
- **Create Topic**: Click "Create Topic" button, specify name, partitions, and replication factor
- **View Topic**: Click on any topic card to see details, messages, and partitions
- **Delete Topic**: Use the delete button with confirmation dialog
- **Add Partitions**: In topic details, use "Add Partitions" feature

### 3. Browsing Messages
- Select a topic from the dropdown
- Specify partition, offset, and limit
- View messages in a paginated table
- Export messages to JSON format

### 4. Producing Messages
- Choose a target topic
- Enter message key (optional) and value
- Add custom headers in JSON format
- Send messages and view history

### 5. Monitoring Consumer Groups
- View all consumer groups with their states
- Check group members and their assignments
- Monitor offset lag per topic/partition
- Delete inactive groups

### 6. Real-time Monitoring
- Enable real-time message streaming on topic detail pages
- WebSocket connection provides live updates
- Auto-refreshing cluster health status

## 🚦 Health Monitoring

The dashboard provides comprehensive health monitoring:

### Connection Status
- Real-time Kafka cluster connectivity
- Broker availability monitoring
- WebSocket connection status

### Performance Metrics
- Message throughput visualization
- Consumer lag monitoring
- Partition distribution analytics

### Alerts & Notifications
- Toast notifications for operations
- Error handling with user feedback
- Connection loss warnings

## 🔒 Security Considerations

### Authentication
Currently, the dashboard doesn't include built-in authentication. For production use:
- Implement authentication middleware
- Use reverse proxy with authentication (nginx, Apache)
- Consider OAuth2 or LDAP integration

### Network Security
- Use HTTPS in production
- Configure proper CORS origins
- Implement rate limiting (included)
- Use VPN or private networks for Kafka access

### Kafka Security
The dashboard supports:
- SSL/TLS encryption
- SASL authentication (PLAIN, SCRAM)
- Custom security configurations

## 📊 Performance Tuning

### Backend Optimization
- Adjust connection timeouts based on network latency
- Configure appropriate rate limiting
- Use connection pooling for high-traffic scenarios

### Frontend Optimization
- Implement pagination for large datasets
- Use React Query caching effectively
- Optimize WebSocket connection management

### Kafka Configuration
- Tune consumer and producer configurations
- Optimize batch sizes for better performance
- Configure appropriate retention policies

## 🐛 Troubleshooting

### Common Issues

#### Cannot Connect to Kafka
1. Verify Kafka brokers are running
2. Check network connectivity
3. Validate broker addresses and ports
4. Ensure security configurations match

#### WebSocket Connection Failed
1. Check firewall settings
2. Verify CORS configuration
3. Ensure Socket.IO compatibility

#### High Memory Usage
1. Limit message batch sizes
2. Implement proper pagination
3. Configure garbage collection
4. Monitor consumer lag

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=debug` in environment variables.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Apache Kafka** for the excellent streaming platform
- **KafkaJS** for the robust Node.js Kafka client
- **Material-UI** for the beautiful React components
- **Socket.IO** for real-time communication

## 📞 Support

For support and questions:
1. Check the troubleshooting section
2. Review existing GitHub issues
3. Create a new issue with detailed information
4. Contact the development team at PolicyBazaar

---

**Built with ❤️ for PolicyBazaar's Kafka infrastructure management** 