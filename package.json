{"name": "kafka-dashboard", "version": "1.0.0", "description": "Comprehensive Kafka Dashboard for topic management, monitoring, and administration", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "install-deps": "npm install && cd backend && npm install && cd ../frontend && npm install", "start:local": "concurrently \"npm run server:local\" \"npm run client\"", "start:qa": "concurrently \"npm run server:qa\" \"npm run client\"", "start:prod": "concurrently \"npm run server:prod\" \"npm run client\"", "server:local": "cd backend && npm run dev:local", "server:qa": "cd backend && npm run dev:qa", "server:prod": "cd backend && npm run dev:prod", "prod:backend": "cd backend && npm run start:prod", "prod:frontend": "cd frontend && npm run build && npx serve -s build -l 3000"}, "keywords": ["kafka", "dashboard", "react", "nodejs", "monitoring"], "author": "PolicyBazaar", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}